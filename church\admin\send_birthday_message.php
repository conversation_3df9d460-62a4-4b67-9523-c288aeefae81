<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Enable error logging for debugging
error_log("Birthday message form submitted by admin ID: " . $_SESSION['admin_id']);
error_log("POST data: " . print_r($_POST, true));

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while sending the message.'
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    $missing_fields = [];
    if (empty($_POST['member_id'])) $missing_fields[] = 'member_id';
    if (empty($_POST['template_id'])) $missing_fields[] = 'template_id';
    if (empty($_POST['subject'])) $missing_fields[] = 'subject';
    if (empty($_POST['recipient_email'])) $missing_fields[] = 'recipient_email';
    if (empty($_POST['recipient_name'])) $missing_fields[] = 'recipient_name';

    if (!empty($missing_fields)) {
        error_log("Birthday message validation failed. Missing fields: " . implode(', ', $missing_fields));
        error_log("POST data: " . print_r($_POST, true));
        $_SESSION['error'] = 'All required fields must be filled out. Missing: ' . implode(', ', $missing_fields);
        $redirect_to = $_POST['redirect_to'] ?? 'birthday';
        $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
        header("Location: $redirect_page");
        exit;
    }

    try {
        // Get form data
        $member_id = $_POST['member_id'];
        $template_id = $_POST['template_id'];
        $subject = $_POST['subject'];
        $custom_message = $_POST['custom_message'] ?? '';
        $recipient_email = $_POST['recipient_email'];
        $recipient_name = $_POST['recipient_name'];

        // If no template selected, try to get the first birthday template
        if (empty($template_id)) {
            $db = isset($pdo) ? $pdo : $conn;
            $stmt = $db->prepare("SELECT id FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
            $stmt->execute();
            $firstTemplate = $stmt->fetch();
            if ($firstTemplate) {
                $template_id = $firstTemplate['id'];
                error_log("No template selected, using first birthday template: " . $template_id);
            } else {
                $_SESSION['error'] = 'No birthday email templates found. Please create a birthday template first.';
                $redirect_to = $_POST['redirect_to'] ?? 'birthday';
                $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
                header("Location: $redirect_page");
                exit;
            }
        }

        // Get the email template
        $db = isset($pdo) ? $pdo : $conn;
        $stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();

        if (!$template) {
            $_SESSION['error'] = 'Email template not found.';
            $redirect_to = $_POST['redirect_to'] ?? 'birthday';
            $redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
            header("Location: $redirect_page");
            exit;
        }

        // Get member details for image path
        $stmt = $db->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            // Create a basic member data array if not found
            $member = [
                'full_name' => $recipient_name,
                'email' => $recipient_email,
                'phone_number' => '',
                'image_path' => '' // No image available
            ];
        }

        // Replace placeholders in the template using the standardized function
        $subject = replaceTemplatePlaceholders($template['subject'], $member);
        $body = replaceTemplatePlaceholders($template['content'], $member);
        
        // Add custom message if provided
        if (!empty($custom_message)) {
            $body = str_replace('{custom_message}', $custom_message, $body);
        } else {
            $body = str_replace('{custom_message}', '', $body);
        }

        // Send the email
        if (sendEmail($recipient_email, $recipient_name, $subject, $body, true, $member)) {
            // Log the email
            $stmt = $db->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, sent_at) VALUES (?, ?, ?, 'success', NOW())");
            $stmt->execute([$member_id, $template_id, $subject]);

            // Generate a unique tracking ID
            $tracking_id = uniqid('track_', true);

            // Create tracking record
            $stmt = $db->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'birthday', NOW())");
            $stmt->execute([$member_id, $tracking_id]);

            $_SESSION['message'] = "Birthday message sent successfully to $recipient_name.";
        } else {
            // Log the failed email
            global $last_email_error;
            $error_message = $last_email_error ?? 'Unknown error';

            $stmt = $db->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, error_message, sent_at) VALUES (?, ?, ?, 'failed', ?, NOW())");
            $stmt->execute([$member_id, $template_id, $subject, $error_message]);

            $_SESSION['error'] = "Failed to send email. Error: $error_message";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = 'Error: ' . $e->getMessage();
    }
}

// Redirect back to appropriate page
$redirect_to = $_POST['redirect_to'] ?? 'birthday';
$redirect_page = ($redirect_to === 'dashboard') ? 'dashboard.php' : 'birthday.php';
header("Location: $redirect_page");
exit;