<?php
require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Structure Analysis</h2>\n";
    
    // Show all tables
    echo "<h3>All Tables:</h3>\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    echo "\n<h3>Foreign Key Constraints:</h3>\n";
    
    // Check foreign key constraints
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = '$dbname' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY TABLE_NAME, COLUMN_NAME
    ");
    
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($constraints as $constraint) {
        echo "Table: {$constraint['TABLE_NAME']}\n";
        echo "  Column: {$constraint['COLUMN_NAME']}\n";
        echo "  References: {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}\n";
        echo "  Constraint: {$constraint['CONSTRAINT_NAME']}\n\n";
    }
    
    // Check members table structure
    echo "<h3>Members Table Structure:</h3>\n";
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} {$column['Null']} {$column['Key']} {$column['Default']}\n";
    }
    
    // Check for tables that reference members
    echo "\n<h3>Tables that reference members:</h3>\n";
    foreach ($constraints as $constraint) {
        if ($constraint['REFERENCED_TABLE_NAME'] === 'members') {
            echo "- {$constraint['TABLE_NAME']}.{$constraint['COLUMN_NAME']} -> members.{$constraint['REFERENCED_COLUMN_NAME']}\n";
        }
    }
    
    // Check for specific problematic tables
    echo "\n<h3>Checking specific tables:</h3>\n";
    
    $problematic_tables = ['email_logs', 'email_tracking', 'volunteer_opportunities', 'volunteer_applications', 'event_rsvps', 'member_skills', 'family_relationships'];
    
    foreach ($problematic_tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            echo "\n$table exists:\n";
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                echo "  - {$column['Field']}: {$column['Type']}\n";
            }
        } catch (PDOException $e) {
            echo "\n$table: DOES NOT EXIST\n";
        }
    }
    
    // Check for any records that might be causing foreign key issues
    echo "\n<h3>Checking for orphaned records:</h3>\n";
    
    if (in_array('volunteer_opportunities', $tables)) {
        try {
            $stmt = $pdo->query("
                SELECT COUNT(*) as count 
                FROM volunteer_opportunities vo 
                LEFT JOIN members m ON vo.contact_person_id = m.id 
                WHERE vo.contact_person_id IS NOT NULL AND m.id IS NULL
            ");
            $orphaned = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "Orphaned volunteer_opportunities records: {$orphaned['count']}\n";
        } catch (PDOException $e) {
            echo "Error checking volunteer_opportunities: " . $e->getMessage() . "\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
