<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navbar Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        /* Mobile-first navbar styling */
        @media (max-width: 1199.98px) {
            .navbar-brand {
                /* Ensure logo is centered on mobile when no text */
                justify-content: center;
            }
            
            .navbar-brand img {
                /* Slightly larger logo on mobile to compensate for missing text */
                height: 45px !important;
            }
            
            .navbar-brand i {
                /* Larger icon on mobile */
                font-size: 1.8rem;
            }
        }

        @media (min-width: 1200px) {
            .navbar-brand {
                /* Normal alignment on larger screens */
                justify-content: flex-start;
            }
        }
        
        .demo-section {
            padding: 40px 0;
            background: #f8f9fa;
        }
        
        .screen-size-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator">
        <span class="d-xl-none d-lg-inline">Large (lg)</span>
        <span class="d-none d-xl-inline">Extra Large (xl)</span>
        <span class="d-lg-none d-md-inline">Medium (md)</span>
        <span class="d-md-none d-sm-inline">Small (sm)</span>
        <span class="d-sm-none">Extra Small (xs)</span>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #007bff, #6c757d);">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="../index.html">
                <!-- Example with icon (when no logo is available) -->
                <i class="bi bi-house-heart me-xl-2"></i>
                <span class="d-none d-xl-inline">Your Organization Name</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="../index.html">
                            <i class="bi bi-house me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event me-1"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="enhanced_donate.php">
                            <i class="bi bi-heart me-1"></i>Donate
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="user/login.php">
                            <i class="bi bi-person me-1"></i>Member Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="demo-section">
        <div class="container">
            <h1>Mobile Navbar Test</h1>
            <p class="lead">This page demonstrates the mobile-optimized navbar behavior.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h3>Mobile Behavior (xs, sm, md, lg)</h3>
                    <ul>
                        <li>✅ Logo/icon only (no organization name)</li>
                        <li>✅ Slightly larger logo/icon size</li>
                        <li>✅ Centered logo alignment</li>
                        <li>✅ Hamburger menu for navigation</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h3>Desktop Behavior (xl and above)</h3>
                    <ul>
                        <li>✅ Logo/icon + organization name</li>
                        <li>✅ Standard logo size</li>
                        <li>✅ Left-aligned brand</li>
                        <li>✅ Full navigation menu visible</li>
                    </ul>
                </div>
            </div>
            
            <div class="alert alert-info mt-4">
                <h5><i class="bi bi-info-circle me-2"></i>Testing Instructions</h5>
                <p class="mb-0">
                    Resize your browser window or use developer tools to test different screen sizes. 
                    The organization name should disappear on screens smaller than 1200px (xl breakpoint).
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
