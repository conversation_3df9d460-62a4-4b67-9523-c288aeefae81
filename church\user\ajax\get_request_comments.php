<?php
/**
 * Get Request Comments AJAX Endpoint
 * 
 * Returns comments for a specific request
 */

session_start();
require_once '../../config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Not authenticated']);
    exit;
}

$requestId = $_GET['request_id'] ?? null;

if (!$requestId) {
    echo json_encode(['success' => false, 'error' => 'Request ID required']);
    exit;
}

try {
    // First check if user can view this request
    $stmt = $pdo->prepare("
        SELECT pr.privacy_level, pr.member_id 
        FROM prayer_requests pr 
        WHERE pr.id = ?
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        echo json_encode(['success' => false, 'error' => 'Request not found']);
        exit;
    }
    
    // Check if user can view this request based on privacy level
    $canView = false;
    if ($request['member_id'] == $_SESSION['user_id']) {
        $canView = true; // Owner can always view
    } elseif ($request['privacy_level'] === 'public') {
        $canView = true; // Public requests visible to all
    } elseif ($request['privacy_level'] === 'members') {
        $canView = true; // Members can view member requests
    }
    
    if (!$canView) {
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
    
    // Get comments for the request
    $stmt = $pdo->prepare("
        SELECT 
            prr.*,
            CASE 
                WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN 
                    COALESCE(a.full_name, a.username, 'Administrator')
                ELSE 
                    COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
            END as commenter_name,
            CASE 
                WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN 'admin'
                ELSE 'member'
            END as commenter_type
        FROM prayer_responses prr
        LEFT JOIN members m ON prr.member_id = m.id
        LEFT JOIN admins a ON prr.admin_id = a.id
        WHERE prr.prayer_request_id = ?
        ORDER BY prr.created_at ASC
    ");
    $stmt->execute([$requestId]);
    $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format comments for display
    foreach ($comments as &$comment) {
        $comment['formatted_time'] = formatCommentTime($comment['created_at']);
        $comment['can_delete'] = ($comment['member_id'] == $_SESSION['user_id'] || 
                                 $comment['admin_id'] == $_SESSION['admin_id'] ?? null);
    }
    
    echo json_encode([
        'success' => true,
        'comments' => $comments
    ]);
    
} catch (Exception $e) {
    error_log("Error getting request comments: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load comments'
    ]);
}

function formatCommentTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y', $time);
    }
}
?>
