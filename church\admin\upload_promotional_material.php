<?php
/**
 * Promotional Material Upload Handler
 * 
 * Handles AJAX uploads of promotional materials for events
 */

session_start();

// Include configuration
require_once '../config.php';
require_once 'includes/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
$is_header_banner = filter_input(INPUT_POST, 'is_header_banner', FILTER_VALIDATE_BOOLEAN);
$alt_text = filter_input(INPUT_POST, 'alt_text', FILTER_SANITIZE_STRING);

if (!$event_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid event ID']);
    exit();
}

if (!isset($_FILES['promotional_file']) || $_FILES['promotional_file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
    exit();
}

try {
    $file = $_FILES['promotional_file'];
    $file_name = $file['name'];
    $file_tmp = $file['tmp_name'];
    $file_size = $file['size'];
    $file_type = $file['type'];
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    // Validate file type
    $allowed_types = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'
    ];
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

    if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and PDF files are allowed.']);
        exit();
    }

    // Validate file size (15MB max)
    $max_size = 15 * 1024 * 1024;
    if ($file_size > $max_size) {
        echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 15MB.']);
        exit();
    }

    // Create upload directories
    $base_dir = '../uploads/events/';
    $promotional_dir = $base_dir . 'promotional/';
    $thumbnails_dir = $base_dir . 'thumbnails/';
    
    foreach ([$base_dir, $promotional_dir, $thumbnails_dir] as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }

    // Generate unique filename
    $unique_name = 'event_' . $event_id . '_' . time() . '_' . uniqid() . '.' . $file_extension;
    
    // Determine file category and target directory
    $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
    $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
    $file_path = $target_dir . $unique_name;

    // Move uploaded file
    if (!move_uploaded_file($file_tmp, $file_path)) {
        echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
        exit();
    }

    // Generate thumbnail for images
    $thumbnail_path = null;
    if ($file_category === 'promotional') {
        $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);
    }

    // If this is set as header banner, unset any existing header banner for this event
    if ($is_header_banner) {
        $stmt = $conn->prepare("UPDATE event_files SET is_header_banner = 0 WHERE event_id = ?");
        $stmt->execute([$event_id]);
    }

    // Save to database
    $stmt = $conn->prepare("
        INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size, 
                               uploaded_by, file_category, is_header_banner, alt_text, display_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    // Get next display order
    $order_stmt = $conn->prepare("SELECT COALESCE(MAX(display_order), 0) + 1 FROM event_files WHERE event_id = ?");
    $order_stmt->execute([$event_id]);
    $display_order = $order_stmt->fetchColumn();
    
    $stmt->execute([
        $event_id,
        $file_name,
        $file_path,
        $file_type,
        $file_size,
        $_SESSION['admin_id'],
        $file_category,
        $is_header_banner ? 1 : 0,
        $alt_text,
        $display_order
    ]);

    $file_id = $conn->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => 'File uploaded successfully',
        'file' => [
            'id' => $file_id,
            'name' => $file_name,
            'category' => $file_category,
            'is_header_banner' => $is_header_banner,
            'thumbnail' => $thumbnail_path,
            'file_path' => $file_path
        ]
    ]);

} catch (Exception $e) {
    // Clean up uploaded file if there was an error
    if (isset($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }
    
    echo json_encode(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
}

// Function to generate thumbnails for images
function generateThumbnail($source_path, $thumbnail_dir, $filename) {
    $thumbnail_path = $thumbnail_dir . 'thumb_' . $filename;
    
    // Get image info
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return null;
    }
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // Calculate thumbnail dimensions (max 300x200)
    $thumb_width = 300;
    $thumb_height = 200;
    
    $ratio = min($thumb_width / $width, $thumb_height / $height);
    $new_width = intval($width * $ratio);
    $new_height = intval($height * $ratio);
    
    // Create source image
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($source_path);
            break;
        default:
            return null;
    }
    
    if (!$source) {
        return null;
    }
    
    // Create thumbnail
    $thumbnail = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
    }
    
    imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Save thumbnail
    $success = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $success = imagejpeg($thumbnail, $thumbnail_path, 85);
            break;
        case IMAGETYPE_PNG:
            $success = imagepng($thumbnail, $thumbnail_path);
            break;
        case IMAGETYPE_GIF:
            $success = imagegif($thumbnail, $thumbnail_path);
            break;
    }
    
    imagedestroy($source);
    imagedestroy($thumbnail);
    
    return $success ? $thumbnail_path : null;
}
?>
