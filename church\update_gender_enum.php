<?php
/**
 * Update gender column to only allow Male and Female options
 */

require_once 'config.php';

echo "Updating gender column to only allow Male and Female...\n\n";

try {
    // First, check current gender values
    $stmt = $pdo->query("SELECT DISTINCT gender FROM members WHERE gender IS NOT NULL");
    $currentValues = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Current gender values in database:\n";
    foreach ($currentValues as $value) {
        echo "- " . ($value ?: 'NULL') . "\n";
    }
    echo "\n";
    
    // Update any non-standard values to NULL (or you could map them to Male/Female)
    $updateCount = 0;
    if (!empty($currentValues)) {
        foreach ($currentValues as $value) {
            if (!in_array($value, ['Male', 'Female'])) {
                echo "Found non-standard gender value: '$value' - setting to NULL\n";
                $stmt = $pdo->prepare("UPDATE members SET gender = NULL WHERE gender = ?");
                $stmt->execute([$value]);
                $updateCount += $stmt->rowCount();
            }
        }
    }
    
    if ($updateCount > 0) {
        echo "Updated $updateCount records with non-standard gender values to NULL\n\n";
    }
    
    // Now modify the column to only allow Male and Female
    echo "Modifying gender column to only allow 'Male' and 'Female'...\n";
    $pdo->exec("ALTER TABLE members MODIFY COLUMN gender ENUM('Male', 'Female') DEFAULT NULL");
    
    echo "✓ Gender column successfully updated!\n\n";
    
    // Verify the change
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'gender'");
    $columnInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($columnInfo) {
        echo "Updated column definition:\n";
        echo "Field: {$columnInfo['Field']}\n";
        echo "Type: {$columnInfo['Type']}\n";
        echo "Null: {$columnInfo['Null']}\n";
        echo "Default: {$columnInfo['Default']}\n";
    }
    
    echo "\nGender column update completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
