<?php
/**
 * Test Event Materials Functionality
 * 
 * This script tests the event materials functionality to ensure everything is working correctly
 */

require_once 'config.php';

echo "<h1>Event Materials System Test</h1>";

try {
    // Test 1: Check if event_files table exists and has correct structure
    echo "<h2>Test 1: Database Structure</h2>";
    
    $stmt = $pdo->query("DESCRIBE event_files");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>event_files table structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $required_columns = ['id', 'event_id', 'file_name', 'file_path', 'file_type', 'file_size', 'uploaded_by', 'upload_date', 'file_category', 'is_header_banner', 'display_order', 'alt_text'];
    $found_columns = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        $found_columns[] = $column['Field'];
    }
    echo "</table>";
    
    echo "<h3>Column Check:</h3>";
    foreach ($required_columns as $req_col) {
        if (in_array($req_col, $found_columns)) {
            echo "✓ $req_col - Found<br>";
        } else {
            echo "✗ $req_col - Missing<br>";
        }
    }
    
    // Test 2: Check if events table exists
    echo "<h2>Test 2: Events Table</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events");
    $result = $stmt->fetch();
    echo "Events table exists with " . $result['count'] . " events.<br>";
    
    // Test 3: Check upload directories
    echo "<h2>Test 3: Upload Directories</h2>";
    $directories = [
        'uploads/events/',
        'uploads/events/promotional/',
        'uploads/events/thumbnails/'
    ];
    
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            echo "✓ $dir - Exists<br>";
        } else {
            echo "✗ $dir - Missing<br>";
            // Try to create it
            if (mkdir($dir, 0755, true)) {
                echo "  → Created successfully<br>";
            } else {
                echo "  → Failed to create<br>";
            }
        }
    }
    
    // Test 4: Check if we can query event materials
    echo "<h2>Test 4: Event Materials Query</h2>";
    try {
        $stmt = $pdo->query("
            SELECT ef.*, e.title as event_title 
            FROM event_files ef 
            JOIN events e ON ef.event_id = e.id 
            LIMIT 5
        ");
        $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Found " . count($materials) . " event materials:<br>";
        foreach ($materials as $material) {
            echo "- " . $material['file_name'] . " (Event: " . $material['event_title'] . ")<br>";
        }
    } catch (PDOException $e) {
        echo "Error querying event materials: " . $e->getMessage() . "<br>";
    }
    
    // Test 5: Test file size formatting function
    echo "<h2>Test 5: File Size Formatting</h2>";
    
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';
        
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    $test_sizes = [1024, 1048576, 1073741824, 500000];
    foreach ($test_sizes as $size) {
        echo "$size bytes = " . formatFileSize($size) . "<br>";
    }
    
    echo "<h2>✅ All Tests Completed</h2>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>1. Go to <a href='admin/events.php'>Admin Events</a> to create a new event</li>";
    echo "<li>2. Upload promotional materials to the event</li>";
    echo "<li>3. Go to <a href='user/events.php'>User Events</a> to view the event as a user</li>";
    echo "<li>4. Check that materials are displayed and downloadable</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2>❌ Database Error</h2>";
    echo "Error: " . $e->getMessage();
} catch (Exception $e) {
    echo "<h2>❌ General Error</h2>";
    echo "Error: " . $e->getMessage();
}
?>
