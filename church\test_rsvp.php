<?php
require_once __DIR__ . '/config.php';

// Test database connection and table structure
echo "<h2>RSVP System Test</h2>";

try {
    // Test database connection
    echo "<p>✓ Database connection successful</p>";
    
    // Check if events table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'events'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ Events table exists</p>";
    } else {
        echo "<p>✗ Events table missing</p>";
    }
    
    // Check if event_rsvps_guests table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'event_rsvps_guests'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✓ Guest RSVP table exists</p>";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE event_rsvps_guests");
        echo "<h3>Guest RSVP Table Structure:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>✗ Guest RSVP table missing - creating now...</p>";
        
        // Create the table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS event_rsvps_guests (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                guest_name VARCHAR(255) NOT NULL,
                guest_email VARCHAR(255) NOT NULL,
                guest_phone VARCHAR(20),
                status ENUM('attending', 'not_attending', 'maybe', 'waitlist') DEFAULT 'attending',
                party_size INT(11) DEFAULT 1,
                special_requirements TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_event_id (event_id),
                INDEX idx_guest_email (guest_email),
                INDEX idx_status (status),
                UNIQUE KEY unique_event_guest (event_id, guest_email),
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
            )
        ");
        echo "<p>✓ Guest RSVP table created successfully</p>";
    }
    
    // Check for sample events
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events WHERE is_active = 1");
    $eventCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Active events in database: " . $eventCount . "</p>";
    
    if ($eventCount > 0) {
        $stmt = $pdo->query("SELECT id, title, event_date FROM events WHERE is_active = 1 ORDER BY event_date ASC LIMIT 3");
        echo "<h3>Sample Events:</h3>";
        echo "<ul>";
        while ($event = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<li>ID: " . $event['id'] . " - " . htmlspecialchars($event['title']) . " (" . $event['event_date'] . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>Test RSVP Handler</h3>";
    echo "<p>You can now test the RSVP functionality by:</p>";
    echo "<ol>";
    echo "<li>Going to <a href='events.php'>events.php</a></li>";
    echo "<li>Clicking the RSVP button on any event</li>";
    echo "<li>Filling out the form and submitting</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<p>✗ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Exception $e) {
    echo "<p>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
