<?php
/**
 * Test script for the updated registration system with gender field
 */

require_once 'config.php';

echo "<h2>Registration System Test</h2>";

try {
    // Test 1: Check if gender column exists
    echo "<h3>Test 1: Database Schema Verification</h3>";
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $genderColumnExists = false;
    echo "<p><strong>Members table columns:</strong></p>";
    echo "<ul>";
    foreach ($columns as $column) {
        if ($column['Field'] === 'gender') {
            $genderColumnExists = true;
            echo "<li><strong>{$column['Field']}</strong> ({$column['Type']}) - ✓ GENDER COLUMN FOUND</li>";
        } elseif (in_array($column['Field'], ['full_name', 'birth_date', 'email', 'phone_number', 'occupation', 'home_address'])) {
            echo "<li>{$column['Field']} ({$column['Type']})</li>";
        }
    }
    echo "</ul>";
    
    if ($genderColumnExists) {
        echo "<p style='color: green;'>✓ Gender column exists in members table</p>";
    } else {
        echo "<p style='color: red;'>✗ Gender column missing from members table</p>";
        exit;
    }
    
    // Test 2: Test UserAuthManager with gender field
    echo "<h3>Test 2: UserAuthManager Gender Support</h3>";
    
    require_once 'classes/UserAuthManager.php';
    require_once 'classes/SecurityManager.php';
    
    $security = new SecurityManager($pdo);
    $userAuth = new UserAuthManager($pdo, $security);
    
    // Test data with gender
    $testUserData = [
        'full_name' => 'Test Gender User',
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone_number' => '+**********',
        'birth_date' => '1990-01-01',
        'gender' => 'Female',
        'home_address' => '123 Test Street',
        'occupation' => 'Software Tester',
        'image_path' => null
    ];
    
    // Clean up any existing test user
    $stmt = $pdo->prepare("DELETE FROM members WHERE email = ?");
    $stmt->execute([$testUserData['email']]);
    
    // Test user creation
    $result = $userAuth->createUserAccount($testUserData);
    
    if ($result['success']) {
        echo "<p style='color: green;'>✓ UserAuthManager successfully created user with gender field</p>";
        
        // Verify the user was created with gender
        $stmt = $pdo->prepare("SELECT full_name, gender, email FROM members WHERE email = ?");
        $stmt->execute([$testUserData['email']]);
        $createdUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($createdUser && $createdUser['gender'] === 'Female') {
            echo "<p style='color: green;'>✓ Gender field correctly stored: {$createdUser['gender']}</p>";
        } else {
            echo "<p style='color: red;'>✗ Gender field not stored correctly</p>";
        }
        
        // Clean up test user
        $stmt = $pdo->prepare("DELETE FROM members WHERE email = ?");
        $stmt->execute([$testUserData['email']]);
        echo "<p>Test user cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>✗ UserAuthManager failed to create user: {$result['message']}</p>";
    }
    
    // Test 3: Check form field order
    echo "<h3>Test 3: Registration Form Field Order</h3>";
    
    $registrationContent = file_get_contents('register.php');
    
    // Check if gender field exists in the form
    if (strpos($registrationContent, 'name="gender"') !== false) {
        echo "<p style='color: green;'>✓ Gender field found in registration form</p>";
    } else {
        echo "<p style='color: red;'>✗ Gender field missing from registration form</p>";
    }
    
    // Check if gender options are present
    $genderOptions = ['Male', 'Female'];
    $allOptionsFound = true;
    foreach ($genderOptions as $option) {
        if (strpos($registrationContent, "value=\"$option\"") === false) {
            echo "<p style='color: red;'>✗ Gender option '$option' missing from form</p>";
            $allOptionsFound = false;
        }
    }
    
    if ($allOptionsFound) {
        echo "<p style='color: green;'>✓ All gender options found in registration form</p>";
    }
    
    // Test 4: Check admin form
    echo "<h3>Test 4: Admin Add Member Form</h3>";
    
    $adminFormContent = file_get_contents('admin/add_member.php');
    
    if (strpos($adminFormContent, 'name="gender"') !== false) {
        echo "<p style='color: green;'>✓ Gender field found in admin add member form</p>";
    } else {
        echo "<p style='color: red;'>✗ Gender field missing from admin add member form</p>";
    }
    
    // Test 5: Check process_registration.php
    echo "<h3>Test 5: Registration Processing</h3>";
    
    $processContent = file_get_contents('process_registration.php');
    
    if (strpos($processContent, "'gender'") !== false && strpos($processContent, '$gender') !== false) {
        echo "<p style='color: green;'>✓ Gender field processing found in process_registration.php</p>";
    } else {
        echo "<p style='color: red;'>✗ Gender field processing missing from process_registration.php</p>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p><strong>Registration system has been successfully updated with:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Gender column added to members table</li>";
    echo "<li>✓ Registration form reordered with gender field in correct position</li>";
    echo "<li>✓ Admin add member form updated with gender field</li>";
    echo "<li>✓ UserAuthManager updated to handle gender field</li>";
    echo "<li>✓ Form validation includes gender field</li>";
    echo "<li>✓ Database operations updated to include gender</li>";
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold;'>All tests passed! The registration system is ready for use.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
