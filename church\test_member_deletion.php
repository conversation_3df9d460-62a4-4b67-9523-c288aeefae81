<?php
require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Member Deletion Test</h2>\n";
    
    // First, let's see what members exist
    echo "<h3>Current Members:</h3>\n";
    $stmt = $pdo->query("SELECT id, full_name, email FROM members ORDER BY id DESC LIMIT 10");
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($members)) {
        echo "No members found in the database.\n";
        exit();
    }
    
    echo "<table border='1'>\n";
    echo "<tr><th>ID</th><th>Name</th><th>Email</th></tr>\n";
    foreach ($members as $member) {
        echo "<tr><td>{$member['id']}</td><td>{$member['full_name']}</td><td>{$member['email']}</td></tr>\n";
    }
    echo "</table>\n";
    
    // Check what tables exist that might reference members
    echo "\n<h3>Tables that might reference members:</h3>\n";
    $tables_to_check = ['email_logs', 'email_tracking', 'volunteer_opportunities', 'volunteer_applications', 'event_rsvps', 'member_skills', 'family_relationships', 'member_gifts'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✓ $table exists\n";
                
                // Check if it has member_id column
                $stmt = $pdo->query("SHOW COLUMNS FROM $table LIKE '%member%'");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                if (!empty($columns)) {
                    echo "  - Member-related columns: " . implode(', ', $columns) . "\n";
                }
            } else {
                echo "✗ $table does not exist\n";
            }
        } catch (PDOException $e) {
            echo "✗ Error checking $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Test deletion logic without actually deleting
    echo "\n<h3>Testing Deletion Logic (Dry Run):</h3>\n";
    
    if (!empty($members)) {
        $test_member_id = $members[0]['id'];
        echo "Testing deletion logic for member ID: $test_member_id ({$members[0]['full_name']})\n\n";
        
        // Helper function to check if table exists
        $checkTable = function($tableName) use ($pdo) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
                return $stmt->rowCount() > 0;
            } catch (PDOException $e) {
                return false;
            }
        };
        
        // Helper function to check if column exists in table
        $checkColumn = function($tableName, $columnName) use ($pdo) {
            try {
                $stmt = $pdo->query("SHOW COLUMNS FROM $tableName LIKE '$columnName'");
                return $stmt->rowCount() > 0;
            } catch (PDOException $e) {
                return false;
            }
        };
        
        $deletion_steps = [];
        
        // Check each table and what would be deleted
        if ($checkTable('email_logs') && $checkColumn('email_logs', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_logs WHERE member_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "email_logs: $count records would be deleted";
        }
        
        if ($checkTable('email_tracking') && $checkColumn('email_tracking', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_tracking WHERE member_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "email_tracking: $count records would be deleted";
        }
        
        if ($checkTable('volunteer_opportunities') && $checkColumn('volunteer_opportunities', 'contact_person_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM volunteer_opportunities WHERE contact_person_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "volunteer_opportunities: $count records would have contact_person_id set to NULL";
        }
        
        if ($checkTable('volunteer_applications') && $checkColumn('volunteer_applications', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM volunteer_applications WHERE member_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "volunteer_applications: $count records would be deleted";
        }
        
        if ($checkTable('event_rsvps') && $checkColumn('event_rsvps', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM event_rsvps WHERE member_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "event_rsvps: $count records would be deleted";
        }
        
        if ($checkTable('member_skills') && $checkColumn('member_skills', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM member_skills WHERE member_id = ?");
            $stmt->execute([$test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "member_skills: $count records would be deleted";
        }
        
        if ($checkTable('family_relationships') && $checkColumn('family_relationships', 'member_id')) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM family_relationships WHERE member_id = ? OR related_member_id = ?");
            $stmt->execute([$test_member_id, $test_member_id]);
            $count = $stmt->fetchColumn();
            $deletion_steps[] = "family_relationships: $count records would be deleted";
        }
        
        if ($checkTable('member_gifts')) {
            if ($checkColumn('member_gifts', 'sender_id')) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM member_gifts WHERE sender_id = ? OR recipient_id = ?");
                $stmt->execute([$test_member_id, $test_member_id]);
                $count = $stmt->fetchColumn();
                $deletion_steps[] = "member_gifts: $count records would be deleted";
            } else if ($checkColumn('member_gifts', 'recipient_id')) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM member_gifts WHERE recipient_id = ?");
                $stmt->execute([$test_member_id]);
                $count = $stmt->fetchColumn();
                $deletion_steps[] = "member_gifts: $count records would be deleted";
            }
        }
        
        echo "Deletion steps that would be performed:\n";
        foreach ($deletion_steps as $step) {
            echo "- $step\n";
        }
        
        echo "\nFinal step: Delete member record from members table\n";
        echo "\n<strong>All checks passed! Member deletion should work properly.</strong>\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
