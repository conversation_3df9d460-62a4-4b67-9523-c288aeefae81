[2025-07-14 14:49:37] === EMAIL DEBUG TEST STARTED ===
[2025-07-14 14:49:37] Checking PHPMailer availability...
[2025-07-14 14:49:37] PHPMailer is available
[2025-07-14 14:49:37] Checking database connection...
[2025-07-14 14:49:37] Database connection successful
[2025-07-14 14:49:37] Checking email settings tables...
[2025-07-14 14:49:37] email_settings table exists
[2025-07-14 14:49:37] Email settings from email_settings table: {
    "contact_email": "",
    "smtp_host": "smtp.hostinger.com",
    "smtp_auth": "1",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_secure": "tls",
    "smtp_port": "465",
    "sender_email": "<EMAIL>",
    "sender_name": "Freedom Assembly Church",
    "reply_to_email": "<EMAIL>",
    "email_sending_delay": "2",
    "email_batch_size": "20",
    "email_sending_delay_seconds": "5",
    "replyToEmail": "<EMAIL>",
    "random_number_1": "20",
    "random_number_2": "36",
    "random_number_3": "42",
    "random_number_4": "79",
    "random_number_5": "86",
    "random_powerball": "9"
}
[2025-07-14 14:49:37] Email-related settings from settings table: {
    "email_smtp_host": "smtp.hostinger.com",
    "email_smtp_auth": "1",
    "email_smtp_username": "<EMAIL>",
    "email_smtp_password": "5:G!&sX5M5r",
    "email_smtp_secure": "ssl",
    "email_smtp_port": "465",
    "email_sender_email": "<EMAIL>",
    "email_sender_name": "Powerball Lotto",
    "email_reply_to_email": "<EMAIL>",
    "admin_email": "<EMAIL>",
    "contact_email": "<EMAIL>",
    "email_contact_email": "",
    "raffle_reply_email": "<EMAIL>",
    "smtp_host": "smtp.hostinger.com",
    "smtp_port": "465",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_encryption": "tls",
    "from_email": "<EMAIL>",
    "reply_to_email": "<EMAIL>",
    "email_signature": "",
    "enable_email_queue": "1",
    "admin_notification_email": "<EMAIL>",
    "email_logo": "assets\/img\/logos\/email_logo.jpg"
}
[2025-07-14 14:49:37] Testing email configuration...
[2025-07-14 14:49:37] Final email settings: {
    "contact_email": "",
    "smtp_host": "smtp.hostinger.com",
    "smtp_auth": "1",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_secure": "tls",
    "smtp_port": "465",
    "sender_email": "<EMAIL>",
    "sender_name": "Freedom Assembly Church",
    "reply_to_email": "<EMAIL>",
    "email_sending_delay": "2",
    "email_batch_size": "20",
    "email_sending_delay_seconds": "5",
    "replyToEmail": "<EMAIL>",
    "random_number_1": "20",
    "random_number_2": "36",
    "random_number_3": "42",
    "random_number_4": "79",
    "random_number_5": "86",
    "random_powerball": "9"
}
[2025-07-14 14:49:37] Attempting to send test <NAME_EMAIL>...
[2025-07-14 14:49:37] SMTP Configuration:
[2025-07-14 14:49:37] Host: smtp.hostinger.com
[2025-07-14 14:49:37] Username: <EMAIL>
[2025-07-14 14:49:37] Password: [HIDDEN]
[2025-07-14 14:49:37] Port: 465
[2025-07-14 14:49:37] Security: tls
[2025-07-14 14:49:37] Auth: YES
[2025-07-14 14:49:37] Attempting to send email...
[2025-07-14 14:51:48] === EMAIL DEBUG TEST STARTED ===
[2025-07-14 14:51:48] Checking PHPMailer availability...
[2025-07-14 14:51:48] PHPMailer is available
[2025-07-14 14:51:48] Checking database connection...
[2025-07-14 14:51:48] Database connection successful
[2025-07-14 14:51:48] Checking email settings tables...
[2025-07-14 14:51:48] email_settings table exists
[2025-07-14 14:51:48] Email settings from email_settings table: {
    "contact_email": "",
    "smtp_host": "smtp.hostinger.com",
    "smtp_auth": "1",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_secure": "tls",
    "smtp_port": "465",
    "sender_email": "<EMAIL>",
    "sender_name": "Freedom Assembly Church",
    "reply_to_email": "<EMAIL>",
    "email_sending_delay": "2",
    "email_batch_size": "20",
    "email_sending_delay_seconds": "5",
    "replyToEmail": "<EMAIL>",
    "random_number_1": "20",
    "random_number_2": "36",
    "random_number_3": "42",
    "random_number_4": "79",
    "random_number_5": "86",
    "random_powerball": "9"
}
[2025-07-14 14:51:48] Email-related settings from settings table: {
    "email_smtp_host": "smtp.hostinger.com",
    "email_smtp_auth": "1",
    "email_smtp_username": "<EMAIL>",
    "email_smtp_password": "5:G!&sX5M5r",
    "email_smtp_secure": "ssl",
    "email_smtp_port": "465",
    "email_sender_email": "<EMAIL>",
    "email_sender_name": "Powerball Lotto",
    "email_reply_to_email": "<EMAIL>",
    "admin_email": "<EMAIL>",
    "contact_email": "<EMAIL>",
    "email_contact_email": "",
    "raffle_reply_email": "<EMAIL>",
    "smtp_host": "smtp.hostinger.com",
    "smtp_port": "465",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_encryption": "tls",
    "from_email": "<EMAIL>",
    "reply_to_email": "<EMAIL>",
    "email_signature": "",
    "enable_email_queue": "1",
    "admin_notification_email": "<EMAIL>",
    "email_logo": "assets\/img\/logos\/email_logo.jpg"
}
[2025-07-14 14:51:48] Testing email configuration...
[2025-07-14 14:51:48] Final email settings: {
    "contact_email": "",
    "smtp_host": "smtp.hostinger.com",
    "smtp_auth": "1",
    "smtp_username": "<EMAIL>",
    "smtp_password": "Jv7M5YMuX$5d",
    "smtp_secure": "tls",
    "smtp_port": "465",
    "sender_email": "<EMAIL>",
    "sender_name": "Freedom Assembly Church",
    "reply_to_email": "<EMAIL>",
    "email_sending_delay": "2",
    "email_batch_size": "20",
    "email_sending_delay_seconds": "5",
    "replyToEmail": "<EMAIL>",
    "random_number_1": "20",
    "random_number_2": "36",
    "random_number_3": "42",
    "random_number_4": "79",
    "random_number_5": "86",
    "random_powerball": "9"
}
[2025-07-14 14:51:48] Attempting to send test <NAME_EMAIL>...
[2025-07-14 14:51:48] Corrected: Using SSL for port 465
[2025-07-14 14:51:48] SMTP Configuration:
[2025-07-14 14:51:48] Host: smtp.hostinger.com
[2025-07-14 14:51:48] Username: <EMAIL>
[2025-07-14 14:51:48] Password: [HIDDEN]
[2025-07-14 14:51:48] Port: 465
[2025-07-14 14:51:48] Security: ssl
[2025-07-14 14:51:48] Auth: YES
[2025-07-14 14:51:48] Attempting to send email...
[2025-07-14 14:51:49] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-14 14:51:49] SMTP Debug [1]: CLIENT -> SERVER: EHLO USER

[2025-07-14 14:51:49] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-14 14:51:49] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-14 14:51:49] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-14 14:51:49] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-14 14:51:49] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-14 14:51:49] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-14 14:51:49] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-14 14:51:49] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-14 14:51:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-14 14:51:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-14 14:51:50] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: Date: Mon, 14 Jul 2025 14:51:48 +0200

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: To: Test Recipient <<EMAIL>>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: Subject: Email System Test - 2025-07-14 14:51:48

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <m7YQlFkzQuCgFpbdMmhrq95PArqtv93PIDjTShSjI@USER>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=iso-8859-1

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:     <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         <h2 style="color: #333;">Email System Test</h2>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         <p>This is a test email to verify that the email system is working correctly.</p>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         <p><strong>Test Details:</strong></p>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         <ul>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:             <li>Sent at: 2025-07-14 14:51:48</li>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:             <li>SMTP Host: smtp.hostinger.com</li>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:             <li>SMTP Port: 465</li>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:             <li>From: <EMAIL></li>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         </ul>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:         <p>If you receive this email, the email system is working correctly.</p>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-14 14:51:50] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-14 14:51:51] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bghz96gVMzFK5M8

[2025-07-14 14:51:51] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-14 14:51:51] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-14 14:51:51] SUCCESS: Email sent <NAME_EMAIL>
[2025-07-14 14:51:51] === EMAIL DEBUG TEST COMPLETED ===
[2025-07-14 14:51:51] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-14 14:51:51] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

