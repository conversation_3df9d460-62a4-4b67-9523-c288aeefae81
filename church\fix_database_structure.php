<?php
require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Structure Fix</h2>\n";
    
    // Disable foreign key checks temporarily
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 1. Create email_logs table if it doesn't exist
    echo "<h3>1. Creating email_logs table...</h3>\n";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS email_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            email_type VARCHAR(100) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('sent', 'failed', 'pending') DEFAULT 'sent',
            error_message TEXT NULL,
            INDEX idx_member_id (member_id),
            INDEX idx_sent_at (sent_at),
            INDEX idx_status (status),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✓ email_logs table created/verified\n";
    } catch (PDOException $e) {
        echo "✗ Error creating email_logs: " . $e->getMessage() . "\n";
    }
    
    // 2. Create email_tracking table if it doesn't exist
    echo "<h3>2. Creating email_tracking table...</h3>\n";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS email_tracking (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            email_id VARCHAR(255) NOT NULL,
            opened_at TIMESTAMP NULL,
            clicked_at TIMESTAMP NULL,
            bounced_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_member_id (member_id),
            INDEX idx_email_id (email_id),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✓ email_tracking table created/verified\n";
    } catch (PDOException $e) {
        echo "✗ Error creating email_tracking: " . $e->getMessage() . "\n";
    }
    
    // 3. Create/fix volunteer_opportunities table
    echo "<h3>3. Creating volunteer_opportunities table...</h3>\n";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS volunteer_opportunities (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            requirements TEXT,
            location VARCHAR(255),
            start_date DATE,
            end_date DATE,
            time_commitment VARCHAR(100),
            contact_person_id INT NULL,
            max_volunteers INT DEFAULT NULL,
            current_volunteers INT DEFAULT 0,
            status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_contact_person (contact_person_id),
            INDEX idx_status (status),
            INDEX idx_start_date (start_date),
            FOREIGN KEY (contact_person_id) REFERENCES members(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✓ volunteer_opportunities table created/verified\n";
    } catch (PDOException $e) {
        echo "✗ Error creating volunteer_opportunities: " . $e->getMessage() . "\n";
    }
    
    // 4. Create volunteer_applications table
    echo "<h3>4. Creating volunteer_applications table...</h3>\n";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS volunteer_applications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            opportunity_id INT NOT NULL,
            member_id INT NOT NULL,
            application_message TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reviewed_at TIMESTAMP NULL,
            reviewed_by INT NULL,
            INDEX idx_opportunity_id (opportunity_id),
            INDEX idx_member_id (member_id),
            INDEX idx_status (status),
            UNIQUE KEY unique_application (opportunity_id, member_id),
            FOREIGN KEY (opportunity_id) REFERENCES volunteer_opportunities(id) ON DELETE CASCADE,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✓ volunteer_applications table created/verified\n";
    } catch (PDOException $e) {
        echo "✗ Error creating volunteer_applications: " . $e->getMessage() . "\n";
    }
    
    // 5. Create event_rsvps table
    echo "<h3>5. Creating event_rsvps table...</h3>\n";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS event_rsvps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            member_id INT NOT NULL,
            status ENUM('attending', 'not_attending', 'maybe') DEFAULT 'attending',
            guest_count INT DEFAULT 0,
            notes TEXT,
            rsvp_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_member_id (member_id),
            INDEX idx_status (status),
            UNIQUE KEY unique_event_member (event_id, member_id),
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        echo "✓ event_rsvps table created/verified\n";
    } catch (PDOException $e) {
        echo "✗ Error creating event_rsvps: " . $e->getMessage() . "\n";
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    echo "<h3>6. Checking for orphaned records...</h3>\n";
    
    // Fix orphaned volunteer_opportunities records
    try {
        $stmt = $pdo->query("
            UPDATE volunteer_opportunities 
            SET contact_person_id = NULL 
            WHERE contact_person_id IS NOT NULL 
            AND contact_person_id NOT IN (SELECT id FROM members)
        ");
        $affected = $stmt->rowCount();
        echo "✓ Fixed $affected orphaned volunteer_opportunities records\n";
    } catch (PDOException $e) {
        echo "✗ Error fixing orphaned records: " . $e->getMessage() . "\n";
    }
    
    echo "\n<h3>Database structure fix completed!</h3>\n";
    echo "<p>You can now try deleting members again.</p>\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
