<?php
/**
 * Database migration script to add gender column to members table
 * This script safely adds the gender column if it doesn't exist
 */

require_once 'config.php';

echo "Adding gender column to members table...\n\n";

try {
    // Check if gender column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM members LIKE 'gender'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "Gender column already exists in members table.\n";
    } else {
        echo "Adding gender column to members table...\n";
        
        // Add gender column after birth_date column
        $pdo->exec("ALTER TABLE members ADD COLUMN gender ENUM('Male', 'Female') DEFAULT NULL AFTER birth_date");
        
        echo "✓ Gender column added successfully!\n";
    }
    
    // Verify the column was added
    $stmt = $pdo->query("DESCRIBE members");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nCurrent members table structure:\n";
    foreach ($columns as $column) {
        if ($column['Field'] === 'gender') {
            echo "✓ {$column['Field']} ({$column['Type']}) - FOUND\n";
        } elseif (in_array($column['Field'], ['full_name', 'birth_date', 'email', 'phone_number', 'occupation', 'home_address'])) {
            echo "  {$column['Field']} ({$column['Type']})\n";
        }
    }
    
    echo "\nGender column migration completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
